import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 使用React.memo优化组件，避免不必要的重渲染
const StreamingOutput = React.memo(({ content, isGenerating = true }) => {
  const outputRef = useRef(null);

  // 使用useMemo优化内容处理，避免每次渲染都重新计算
  const processedContent = useMemo(() => {
    if (!content) return { text: '', wordCount: 0, lineCount: 0, progress: 0 };

    // 计算生成进度（基于内容长度的简单估算）
    const estimatedProgress = Math.min((content.length / 5000) * 100, 95);

    return {
      text: content,
      progress: estimatedProgress,
      wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
      lineCount: content.split('\n').length
    };
  }, [content]);

  // 使用useCallback优化滚动函数
  const scrollToBottom = useCallback(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, []);

  // 当内容更新时自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [content, scrollToBottom]);

  // 自定义Markdown组件，使用useMemo缓存避免重复创建
  const markdownComponents = useMemo(() => ({
    h1: ({ children }) => (
      <Typography variant="h4" sx={{ mb: 2, color: '#1a73e8', fontWeight: 600 }}>
        {children}
      </Typography>
    ),
    h2: ({ children }) => (
      <Typography variant="h5" sx={{ mb: 1.5, mt: 2, color: '#1a73e8', fontWeight: 600 }}>
        {children}
      </Typography>
    ),
    h3: ({ children }) => (
      <Typography variant="h6" sx={{ mb: 1, mt: 1.5, color: '#1a73e8', fontWeight: 600 }}>
        {children}
      </Typography>
    ),
    p: ({ children }) => (
      <Typography variant="body1" sx={{ mb: 1, lineHeight: 1.6 }}>
        {children}
      </Typography>
    ),
    strong: ({ children }) => (
      <Typography component="span" sx={{ fontWeight: 600, color: '#1a73e8' }}>
        {children}
      </Typography>
    )
  }), []);

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      width: '100%',
      overflow: 'hidden' // 防止整体容器溢出
    }}>
      {/* 进度指示器和统计信息 */}
      <Box sx={{ mb: 2, flexShrink: 0 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {isGenerating ? '🤖 AI正在生成内容...' : '✅ 生成完成'}
          </Typography>

          {processedContent.text && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={`${processedContent.wordCount} 词`}
                size="small"
                variant="outlined"
                color="primary"
              />
              <Chip
                label={`${processedContent.lineCount} 行`}
                size="small"
                variant="outlined"
                color="secondary"
              />
            </Box>
          )}
        </Box>

        {isGenerating && (
          <LinearProgress
            variant={processedContent.progress > 0 ? "determinate" : "indeterminate"}
            value={processedContent.progress}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: '#e3f2fd',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                background: 'linear-gradient(45deg, #1a73e8 30%, #4285f4 90%)'
              }
            }}
          />
        )}
      </Box>

      {/* 内容显示区域 */}
      <Box
        ref={outputRef}
        sx={{
          backgroundColor: '#f5f5f5',
          borderRadius: 1,
          p: 2,
          flex: 1, // 让内容区域填满剩余空间
          minHeight: 0, // 重要：允许flex子项收缩到内容以下
          overflowY: 'auto',
          overflowX: 'hidden', // 防止水平滚动
          wordBreak: 'break-word',
          position: 'relative',
          border: '1px solid #e0e0e0',
          transition: 'all 0.3s ease',
          boxSizing: 'border-box', // 确保padding计算正确
          '&:hover': {
            borderColor: '#1a73e8'
          },
          // 自定义滚动条样式
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '4px',
            '&:hover': {
              background: '#a8a8a8',
            },
          },
        }}
      >
        {processedContent.text ? (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={markdownComponents}
          >
            {processedContent.text}
          </ReactMarkdown>
        ) : (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 100,
            color: 'text.secondary'
          }}>
            <Typography variant="body2">
              {isGenerating ? '⏳ 等待AI响应...' : '📝 暂无内容'}
            </Typography>
          </Box>
        )}

        {/* 实时打字效果指示器 */}
        {isGenerating && processedContent.text && (
          <Box
            sx={{
              display: 'inline-block',
              width: 2,
              height: 20,
              backgroundColor: '#1a73e8',
              animation: 'blink 1s infinite',
              '@keyframes blink': {
                '0%, 50%': { opacity: 1 },
                '51%, 100%': { opacity: 0 }
              }
            }}
          />
        )}
      </Box>
    </Box>
  );
});

// 设置displayName用于调试
StreamingOutput.displayName = 'StreamingOutput';

export default StreamingOutput;
